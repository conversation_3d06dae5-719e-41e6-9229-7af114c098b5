# OutlookRegister  

Outlook 注册机  
不保证可用性，自行测试。 

1. 模拟人类填表操作  
2. 自动过验证码  
3. 注册成功（长效账号）

你需要做的内容只有：  

1.使用本地代理IP**搭建代理池**。  
2.在`config.json`填写你的**浏览器目录**和**代理**，并调整数量与最大注册量。  
3.如果你需要Oauth2，请在`config.json`中修改`"enable_oauth2"`的值为`true`并填写`Scopes`与`redirect_url`。  

注意事项：  
选用好的**IP**与**浏览器**，否则可能过不去检测，同一IP短时间不宜多次注册。
我觉得浏览器**至少**要能过机器人检测网站，**并非**只要是所谓的指纹浏览器就行。
邮箱自动存储到工作目录的`Results`下。  
如果使用无头模式，请自己注意反爬的应对手段。  
高并发还是得走协议。 

我试着登陆了十几个之前注册的账号(没开oauth2)，全是长效账号，还是相当不错的。  
之后看看能不能减少注册时被检测的概率。   

使用教程：  
```python OutlookRegister.py```
