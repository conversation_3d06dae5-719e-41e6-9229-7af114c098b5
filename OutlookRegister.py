import os
import time
import json
import random
import string
import secrets
from datetime import datetime
from faker import Faker
from get_token import get_access_token
from playwright.sync_api import sync_playwright
from concurrent.futures import ThreadPoolExecutor

# 全局变量记录账号信息
success_accounts = []
failed_accounts = []

def save_final_results():
    """保存最终结果到带时间戳的文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    success_count = len(success_accounts)
    failed_count = len(failed_accounts)

    filename = f"Results/结果_{timestamp}_成功{success_count}个_失败{failed_count}个.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"Outlook注册结果统计\n")
        f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总计: {success_count + failed_count}个\n")
        f.write(f"成功: {success_count}个\n")
        f.write(f"失败: {failed_count}个\n")
        f.write("=" * 50 + "\n\n")

        if success_accounts:
            f.write("✅ 注册成功的账号:\n")
            f.write("-" * 50 + "\n")
            for i, account in enumerate(success_accounts, 1):
                oauth_info = " (已获取Token)" if account.get('oauth2') else ""
                f.write(f"{i}. 账号：{account['email']}-----密码：{account['password']}-----cookie：{account.get('cookies', '')}{oauth_info}\n")
            f.write("\n")

        if failed_accounts:
            f.write("❌ 注册失败的账号:\n")
            f.write("-" * 50 + "\n")
            for i, account in enumerate(failed_accounts, 1):
                f.write(f"{i}. 账号：{account['email']}-----密码：{account['password']}-----失败原因：{account['error']}\n")
            f.write("\n")

    print(f"[Info] 详细结果已保存到: {filename}")
    return filename

def generate_strong_password(length=16):

    chars = string.ascii_letters + string.digits + "!@#$%^&*"

    while True:
        password = ''.join(secrets.choice(chars) for _ in range(length))

        if (any(c.islower() for c in password) 
                and any(c.isupper() for c in password)
                and any(c.isdigit() for c in password)
                and any(c in "!@#$%^&*" for c in password)):
            return password


def random_email(length):

    first_char = random.choice(string.ascii_lowercase)

    other_chars = []
    for _ in range(length - 1):  
        if random.random() < 0.07:  
            other_chars.append(random.choice(string.digits))
        else: 
            other_chars.append(random.choice(string.ascii_lowercase))

    return first_char + ''.join(other_chars)

def OpenBrowser(browser_path, proxy, headless):
    """
    启动浏览器：
    - 若 browser_path 提供且存在，则按该路径启动（通常用于指纹浏览器/定制浏览器）。
    - 否则使用 Playwright 自带的 Chromium（需事先执行 `python -m playwright install chromium`）。
    """
    p = None
    browser = None
    try:
        p = sync_playwright().start()

        launch_kwargs = {
            "headless": headless,
            "proxy": {
                "server": proxy,
                "bypass": "localhost",
            },
            "args": [
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--incognito",  # 无痕模式
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
        }

        # 仅当提供了有效路径时，才传 executable_path，避免传空字符串导致 spawn . ENOENT
        if browser_path and isinstance(browser_path, str) and browser_path.strip():
            launch_kwargs["executable_path"] = browser_path

        browser = p.chromium.launch(**launch_kwargs)
        return browser, p

    except Exception as e:
        print(f"[Error] 浏览器启动失败: {str(e)}")
        # 清理已分配的资源
        if browser:
            try:
                browser.close()
            except Exception:
                pass
        if p:
            try:
                p.stop()
            except Exception:
                pass
        raise  # 重新抛出异常，让调用者决定如何处理

def Outlook_register(page, email, password, task_id=1, total_tasks=1):

    fake = Faker()

    lastname = fake.last_name()
    firstname = fake.first_name()
    year = str(random.randint(1960, 2005))
    month = str(random.randint(1, 12))
    day = str(random.randint(1, 28))

    try:
        page.goto("https://outlook.live.com/mail/0/?prompt=create_account", timeout=20000, wait_until="domcontentloaded")
        page.get_by_text('同意并继续').wait_for(timeout=30000)
        start_time = time.time()
        page.wait_for_timeout(step_delay_ms)
        page.get_by_text('同意并继续').click(timeout=30000)

    except Exception as e:
        print(f"[任务 {task_id}/{total_tasks}] 无法进入注册界面: {str(e)}")
        return False
    
    try:
        page.locator('[aria-label="新建电子邮件"]').type(email,delay=80,timeout=10000)
        page.wait_for_timeout(step_delay_ms)
        page.locator('[data-testid="primaryButton"]').click(timeout=5000)
        page.wait_for_timeout(step_delay_ms)
        page.locator('[type="password"]').type(password,delay=60,timeout=10000)
        page.wait_for_timeout(step_delay_ms)
        page.locator('[data-testid="primaryButton"]').click(timeout=5000)

        page.wait_for_timeout(step_delay_ms)
        page.locator('[name="BirthYear"]').fill(year,timeout=10000)

        try:

            page.wait_for_timeout(step_delay_ms)
            page.locator('[name="BirthMonth"]').select_option(value=month,timeout=2000)
            page.wait_for_timeout(step_delay_ms)
            page.locator('[name="BirthDay"]').select_option(value=day)
        
        except:
            # {{ AURA-X: Modify - 修复动态下拉菜单元素分离问题，使用更稳定的选择策略。 }}
            # 处理月份选择
            try:
                page.locator('[name="BirthMonth"]').click()
                page.wait_for_timeout(step_delay_ms)

                # 等待下拉选项出现并稳定
                page.wait_for_selector('[role="option"]', timeout=5000)
                page.wait_for_timeout(500)  # 额外等待确保选项稳定

                # 使用更稳定的选择方式
                month_option = page.locator(f'[role="option"]:text-is("{month}月")')
                month_option.wait_for(state="visible", timeout=5000)
                month_option.click(force=True)  # 强制点击避免被遮挡

            except Exception as month_error:
                print(f"月份选择失败，尝试备用方法: {month_error}")
                # 备用方法：使用键盘导航
                page.locator('[name="BirthMonth"]').focus()
                for _ in range(int(month)):
                    page.keyboard.press('ArrowDown')
                page.keyboard.press('Enter')

            page.wait_for_timeout(step_delay_ms)

            # 处理日期选择
            try:
                page.locator('[name="BirthDay"]').click()
                page.wait_for_timeout(step_delay_ms)

                # 等待下拉选项出现并稳定
                page.wait_for_selector('[role="option"]', timeout=5000)
                page.wait_for_timeout(500)  # 额外等待确保选项稳定

                # 使用更稳定的选择方式
                day_option = page.locator(f'[role="option"]:text-is("{day}日")')
                day_option.wait_for(state="visible", timeout=5000)
                day_option.click(force=True)  # 强制点击避免被遮挡

            except Exception as day_error:
                print(f"日期选择失败，尝试备用方法: {day_error}")
                # 备用方法：使用键盘导航
                page.locator('[name="BirthDay"]').focus()
                for _ in range(int(day)):
                    page.keyboard.press('ArrowDown')
                page.keyboard.press('Enter')

        page.locator('[data-testid="primaryButton"]').click(timeout=5000)

        page.locator('#lastNameInput').type(lastname,delay=120,timeout=10000)
        page.wait_for_timeout(step_delay_ms)
        page.locator('#firstNameInput').fill(firstname,timeout=10000)

        if time.time() - start_time < bot_protection_wait:
            page.wait_for_timeout((bot_protection_wait - time.time() + start_time)*1000)

        page.locator('[data-testid="primaryButton"]').click(timeout=5000)

        page.wait_for_timeout(3000)

        # 检查页面状态，判断是否需要验证码
        try:
            # 方法1：检查是否有验证码iframe
            if page.locator('iframe[src*="hsprotect"]').count() > 0:
                # 直接跳转到验证码处理
                page.wait_for_event("request", lambda req: req.url.startswith("blob:https://iframe.hsprotect.net/"), timeout=5000)
            else:
                # 方法2：等待特定元素消失（原逻辑）
                page.locator('span > [href="https://go.microsoft.com/fwlink/?LinkID=521839"]').wait_for(state='detached', timeout=10000)
        except Exception as e:
            # 继续执行，可能已经跳转了
            pass

        page.wait_for_timeout(step_delay_ms)

        if page.get_by_text('一些异常活动').count() > 0:
            print("[Error: IP or broswer] - 当前IP注册频率过快。检查IP与是否为指纹浏览器并关闭了无头模式。")
            return False

        if page.locator('iframe#enforcementFrame').count() > 0:
            print("[Error: FunCaptcha] - 验证码类型错误，非按压验证码。 ")
            return False

        # 直接检查页面元素，不等待验证码加载
        page.wait_for_timeout(random.randint(2000, 3000))

        # 检查是否有验证码相关元素
        captcha_selectors = [
            'iframe[src*="hsprotect"]',
            '[id*="captcha"]',
            '[class*="captcha"]',
            '[id*="verify"]',
            '[class*="verify"]',
            'iframe[title*="验证"]',
            'iframe[title*="verification"]'
        ]

        has_captcha = False
        for selector in captcha_selectors:
            if page.locator(selector).count() > 0:
                has_captcha = True
                break

        if not has_captcha:
            return True

        # {{ AURA-X: Modify - 优化验证码处理逻辑，精确定位验证码按钮，避免重试时选择错误按钮 }}
        for attempt in range(0, max_captcha_retries + 1):
            print(f"[任务 {task_id}/{total_tasks}] 验证码处理尝试 {attempt + 1}/{max_captcha_retries + 1}")

            try:
                # 方法1：尝试直接定位验证码iframe并处理
                captcha_iframe = page.locator('iframe[src*="hsprotect"]').first
                if captcha_iframe.count() > 0:
                    # 等待iframe加载完成
                    captcha_iframe.wait_for(state="visible", timeout=5000)
                    page.wait_for_timeout(random.randint(1000, 2000))

                    # 点击iframe区域激活验证码
                    captcha_iframe.click()
                    page.wait_for_timeout(random.randint(2000, 3000))

                    # 在 iframe 内通过“长按 Enter”触发按压检测（约8秒）  Confirmed via 寸止
                    # 用户经验：先按两次 Tab 聚焦到按压按钮
                    for _ in range(2):
                        page.keyboard.press('Tab')
                        page.wait_for_timeout(random.randint(200, 400))

                    # 长按 Enter 以触发检测；保持约 8 秒再抬起
                    page.keyboard.down('Enter')
                    page.wait_for_timeout(8000 + random.randint(-400, 400))
                    page.keyboard.up('Enter')
                    page.wait_for_timeout(random.randint(1500, 2500))

                else:
                    # 方法2：使用更精确的Tab导航
                    # 重置焦点到页面顶部
                    page.keyboard.press('Home')
                    page.wait_for_timeout(500)

                    # 寻找验证码相关的可交互元素
                    captcha_selectors = [
                        '[id*="captcha"]',
                        '[class*="captcha"]',
                        '[id*="verify"]',
                        '[class*="verify"]',
                        'button[type="button"]',  # 通用按钮选择器
                        '[role="button"]'
                    ]

                    captcha_found = False
                    for selector in captcha_selectors:
                        elements = page.locator(selector)
                        if elements.count() > 0:
                            try:
                                # 尝试点击第一个匹配的元素
                                elements.first.click(timeout=3000)
                                captcha_found = True
                                page.wait_for_timeout(random.randint(2000, 3000))
                                break
                            except:
                                continue

                    if not captcha_found:
                        # 备用方法：使用 Tab 两次聚焦到按压按钮（用户经验）  Confirmed via 寸止
                        for _ in range(2):
                            page.keyboard.press('Tab')
                            page.wait_for_timeout(random.randint(800, 1200))

                        # 长按 Enter 约 8 秒以触发按压检测
                        page.keyboard.down('Enter')
                        page.wait_for_timeout(8000 + random.randint(-400, 400))
                        page.keyboard.up('Enter')
                        page.wait_for_timeout(random.randint(1500, 2500))

                # 尝试提交验证结果
                # 寻找提交按钮
                submit_selectors = [
                    '[data-testid="primaryButton"]',
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:has-text("提交")',
                    'button:has-text("Submit")',
                    'button:has-text("验证")',
                    'button:has-text("Verify")'
                ]

                submitted = False
                for selector in submit_selectors:
                    try:
                        submit_btn = page.locator(selector)
                        if submit_btn.count() > 0 and submit_btn.first.is_visible():
                            submit_btn.first.click(timeout=3000)
                            submitted = True
                            break
                    except:
                        continue

                if not submitted:
                    # 备用提交方法：按Enter
                    page.keyboard.press('Enter')

                page.wait_for_timeout(random.randint(2000, 4000))

            except Exception as e:
                print(f"[任务 {task_id}/{total_tasks}] 验证码处理异常: {str(e)}")
                # 继续尝试下一次

            # {{ AURA-X: Modify - 增强验证结果检查逻辑，提供更详细的状态反馈 }}
            # 检查验证结果
            page.wait_for_timeout(random.randint(3000, 5000))

            # 多种方式检查验证状态
            verification_success = False
            verification_method = ""

            try:
                # 方法1：检查是否有成功的网络请求
                page.wait_for_event("request", lambda req: req.url.startswith("https://browser.events.data.microsoft.com"), timeout=10000)
                verification_success = True
                verification_method = "网络请求检测"
            except:
                pass

            # 方法2：检查页面URL变化
            current_url = page.url
            if "outlook.live.com" in current_url and "create_account" not in current_url:
                verification_success = True
                verification_method = "URL变化检测"

            # 方法3：检查页面元素
            try:
                # 检查是否有异常活动提示
                if page.get_by_text('一些异常活动').count() > 0:
                    print(f"[任务 {task_id}/{total_tasks}] 检测到异常活动提示，注册失败")
                    return False

                # 检查是否还在验证码页面
                captcha_count = page.locator('iframe[src*="hsprotect"], [id*="captcha"], [class*="captcha"]').count()
                if captcha_count > 0:
                    print(f"[任务 {task_id}/{total_tasks}] 仍在验证码页面，继续重试")
                    continue

                # 检查是否有成功指示器
                success_indicators = [
                    '[data-testid="success"]',
                    '.success',
                    '[class*="success"]',
                    'text=欢迎',
                    'text=Welcome',
                    'text=继续',
                    'text=Continue',
                    '[data-testid="primaryButton"]:has-text("继续")',
                    '[data-testid="primaryButton"]:has-text("Continue")'
                ]

                for indicator in success_indicators:
                    if page.locator(indicator).count() > 0:
                        verification_success = True
                        verification_method = f"页面元素检测: {indicator}"
                        break

                # 方法4：检查是否已经跳转到下一步
                if not verification_success:
                    # 检查是否有下一步的表单元素
                    next_step_indicators = [
                        '[name="BirthYear"]',  # 生日表单
                        '[name="firstName"]',   # 姓名表单
                        '[data-testid="secondaryButton"]',  # 跳过按钮
                        'text=保持登录状态',
                        'text=Stay signed in'
                    ]

                    for indicator in next_step_indicators:
                        if page.locator(indicator).count() > 0:
                            verification_success = True
                            verification_method = f"下一步检测: {indicator}"
                            break

            except Exception as e:
                print(f"[任务 {task_id}/{total_tasks}] 验证状态检查异常: {str(e)}")

            # 判断是否成功
            if verification_success:
                print(f"[任务 {task_id}/{total_tasks}] 验证码验证成功 (方法: {verification_method})")
                page.wait_for_timeout(step_delay_ms)
                break
            else:
                print(f"[任务 {task_id}/{total_tasks}] 验证码验证失败，准备重试")
                # 在重试前稍作等待，避免过于频繁的请求
                page.wait_for_timeout(random.randint(2000, 4000))
                continue

        else:
            raise TimeoutError

    except Exception as e:
        print(f"[任务 {task_id}/{total_tasks}] 注册过程异常: {str(e)}")

        # 检查具体的失败原因
        try:
            if page.get_by_text('一些异常活动').count() > 0:
                print(f"[任务 {task_id}/{total_tasks}] 检测到异常活动提示 - IP可能被限制")
            elif page.get_by_text('无法访问此网站').count() > 0:
                print(f"[任务 {task_id}/{total_tasks}] 网络连接问题")
            elif page.get_by_text('请稍后重试').count() > 0:
                print(f"[任务 {task_id}/{total_tasks}] 服务器要求稍后重试")
            elif "timeout" in str(e).lower():
                print(f"[任务 {task_id}/{total_tasks}] 页面加载超时 - 可能是网络或IP问题")
            else:
                print(f"[任务 {task_id}/{total_tasks}] 未知错误，可能是反机器人检测")
        except:
            pass

        print(f"[任务 {task_id}/{total_tasks}] 注册失败")
        return False
    
    # 按照指定格式保存到文件（不包含Cookie）
    filename = 'Results\\logged_email.txt' if enable_oauth2 else 'Results\\unlogged_email.txt'
    with open(filename, 'a', encoding='utf-8') as f:
        f.write(f"账号：{email}@outlook.com-----密码：{password}\n")

    # 记录到全局成功列表
    success_accounts.append({
        'email': f"{email}@outlook.com",
        'password': password,
        'oauth2': enable_oauth2
    })

    # 成功提示：邮箱与保存位置
    print(f"[Success] 邮箱注册成功：{email}@outlook.com，已保存到 {filename}")

    if not enable_oauth2:
        return True

    try:
        page.locator('[data-testid="secondaryButton"]').click(timeout=20000) 
        button = page.locator('[data-testid="secondaryButton"]')
        button.wait_for(timeout=5000)

    except:

        print(f"[Error: Timeout] - 无法找到按钮。")
        return False   

    try:

        page.wait_for_timeout(random.randint(1600,2000))
        button.click(timeout=6000)
        button = page.locator('[data-testid="secondaryButton"]')
        button.wait_for(timeout=5000)
        page.wait_for_timeout(random.randint(1600,2000))
        button.click(timeout=6000)
        button = page.locator('[data-testid="secondaryButton"]')
        button.wait_for(timeout=5000)
        page.wait_for_timeout(3000)
        button.click(timeout=6000)

    except:
        pass

    try:

        page.wait_for_timeout(3200)
        if page.get_by_text("保持登录状态?").count() > 0:
            page.get_by_text('否').click(timeout=12000)
        page.locator('.splitPrimaryButton[aria-label="新邮件"]').wait_for(timeout=26000)
        return True

    except:
        print(f'[Error: Timeout] - 邮箱未初始化，无法正常收件。')
        return False

def process_single_flow(task_id, total_tasks):
    """
    单个流程：打开浏览器 -> 注册 ->（可选）获取token。
    修复点：
    - 传入 OpenBrowser 的必要参数（browser_path, proxy），避免 TypeError。
    - 在 finally 中做空值判断，避免对 None 调用 close()/stop() 引发 AttributeError。
    """
    browser = None
    p = None
    page = None
    try:
        try:
            browser, p = OpenBrowser(browser_path, proxy, headless)
        except Exception as e:
            print(f"[任务 {task_id}/{total_tasks}] 浏览器启动失败: {str(e)}")
            return False

        try:
            page = browser.new_page()
        except Exception as e:
            print(f"[任务 {task_id}/{total_tasks}] 创建页面失败: {str(e)}")
            return False

        email = random_email(random.randint(12, 14))
        password = generate_strong_password(random.randint(11, 15))
        print(f"[任务 {task_id}/{total_tasks}] 正在注册：{email}@outlook.com")

        try:
            result = Outlook_register(page, email, password, task_id, total_tasks)
        except Exception as e:
            print(f"[任务 {task_id}/{total_tasks}] 注册过程异常: {str(e)}")
            # 记录失败账号
            failed_accounts.append({
                'email': f"{email}@outlook.com",
                'password': password,
                'error': str(e)
            })
            return False
        if result and not enable_oauth2:
            return True
        elif not result:
            # 记录失败账号
            failed_accounts.append({
                'email': f"{email}@outlook.com",
                'password': password,
                'error': '注册失败'
            })
            return False

        token_result = get_access_token(page, email)
        if token_result[0]:
            refresh_token, access_token, expire_at = token_result
            with open(r'Results\outlook_token.txt', 'a') as f2:
                f2.write(email + "@outlook.com---" + password + "---" + refresh_token + "---" + access_token + "---" + str(expire_at) + "\n")
            # 成功提示：Token 保存位置
            print(f"[任务 {task_id}/{total_tasks}] Token 获取成功：{email}@outlook.com，已保存到 Results\\outlook_token.txt")
            return True
        else:
            # 记录失败账号
            failed_accounts.append({
                'email': f"{email}@outlook.com",
                'password': password,
                'error': 'Token获取失败'
            })
            return False

    except Exception as e:
        print(f"[任务 {task_id}/{total_tasks}] 流程异常: {str(e)}")
        # 记录失败账号
        failed_accounts.append({
            'email': f"{email}@outlook.com",
            'password': password,
            'error': f'流程异常: {str(e)}'
        })
        return False

    finally:
        # {{ AURA-X: Modify - 为避免 AttributeError，对资源释放做空值保护。Confirmed via 寸止。 }}
        try:
            if page:
                page.close()
        except Exception:
            pass
        try:
            if browser:
                browser.close()
        except Exception:
            pass
        try:
            if p:
                p.stop()
        except Exception:
            pass

def main(concurrent_flows=10, max_tasks=1000):

    # 启动信息
    print("[Start] 任务开始：")
    print(f"  - 并发数: {concurrent_flows}")
    print(f"  - 任务总数: {max_tasks}")
    print(f"  - 无头模式: {headless}")
    print(f"  - 浏览器: {browser_path}")
    print(f"  - 代理: {proxy}")

    task_counter = 0
    succeeded_tasks = 0
    failed_tasks = 0

    with ThreadPoolExecutor(max_workers=concurrent_flows) as executor:
        running_futures = set()

        while task_counter < max_tasks or len(running_futures) > 0:

            done_futures = {f for f in running_futures if f.done()}
            for future in done_futures:
                try:
                    result = future.result()
                    if result:
                        succeeded_tasks += 1
                    else:
                        failed_tasks += 1

                except Exception as e:
                    failed_tasks += 1
                    print(e)

                running_futures.remove(future)
            
            while len(running_futures) < concurrent_flows and task_counter < max_tasks:
                time.sleep(0.2)
                task_counter += 1
                new_future = executor.submit(process_single_flow, task_counter, max_tasks)
                running_futures.add(new_future)

            time.sleep(0.5)

        print(f"[Info: Result] - 共 {max_tasks} 个，成功 {succeeded_tasks}，失败 {failed_tasks}")

        # 保存详细结果到文件
        save_final_results()

        # 创建Cookie使用说明
        if success_accounts:
            create_cookie_usage_example()

        # 在控制台显示账号信息
        if success_accounts:
            print("\n✅ 注册成功的账号:")
            for i, account in enumerate(success_accounts, 1):
                oauth_info = " (已获取Token)" if account.get('oauth2') else ""
                print(f"  {i}. 账号：{account['email']}-----密码：{account['password']}{oauth_info}")

        if failed_accounts:
            print("\n❌ 注册失败的账号:")
            for i, account in enumerate(failed_accounts, 1):
                print(f"  {i}. 账号：{account['email']}-----密码：{account['password']}-----失败原因：{account['error']}")

        return succeeded_tasks, failed_tasks

if __name__ == '__main__':


    with open('config.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    os.makedirs("Results", exist_ok=True)

    browser_path = data['browser_path']
    bot_protection_wait = data['Bot_protection_wait']
    max_captcha_retries = data['max_captcha_retries']
    proxy = data['proxy']
    enable_oauth2 = data['enable_oauth2']
    concurrent_flows = data["concurrent_flows"]
    max_tasks = data["max_tasks"]
    headless = bool(data.get("headless", True))
    step_delay_ms = int(float(data.get("step_delay", 1)) * 1000)


    main(concurrent_flows, max_tasks)