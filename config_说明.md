# OutlookRegister 配置文件说明

## 配置文件：config.json

### 浏览器配置
- **browser_path**: 浏览器可执行文件路径
  - 支持 Chrome/Edge/指纹浏览器
  - 留空则使用 Playwright 内置 Chromium
  - 示例：`"C:/Program Files/Google/Chrome/Application/chrome.exe"`
  - 指纹浏览器示例：`"D:/AdsPower Global/AdsPower Global.exe"`

- **headless**: 是否开启无头模式
  - `true`: 不显示浏览器窗口（后台运行）
  - `false`: 显示浏览器窗口（推荐，便于手动处理验证码）

### 网络配置
- **proxy**: 代理服务器地址
  - 格式：`http://ip:port` 或 `socks5://ip:port`
  - 建议使用住宅IP代理提高成功率
  - 示例：`"http://127.0.0.1:7897"`

### 任务配置
- **concurrent_flows**: 并发注册数量
  - 同时运行的注册任务数
  - 建议值：1-5个
  - 过多容易被检测和IP限制

- **max_tasks**: 总任务数量
  - 总共要注册的邮箱数量
  - 可以设置为任意正整数

### 反机器人检测配置
- **Bot_protection_wait**: 机器人保护等待时间（秒）
  - 注册过程中的最小等待时间
  - 建议值：15-25秒
  - 过短容易被检测，过长影响效率

- **max_captcha_retries**: 验证码最大重试次数
  - 验证码失败后的重试次数
  - 建议值：5-10次
  - 验证码需要手动完成

- **step_delay**: 操作步骤延迟（秒）
  - 每个操作步骤之间的等待时间
  - 建议值：1.5-3秒
  - 避免操作过快被检测

### OAuth2 Token配置（可选）
- **enable_oauth2**: 是否启用OAuth2获取Token
  - `true`: 获取可用于Microsoft Graph API的Token
  - `false`: 仅注册邮箱账号，不获取Token

- **client_id**: Azure应用程序ID
  - 需要在 Azure Portal 注册应用获取
  - enable_oauth2为true时必填
  - 获取方法：访问 https://portal.azure.com → 应用注册

- **redirect_url**: OAuth2重定向地址
  - 通常设为：`http://localhost:8080/callback`
  - 必须与Azure应用配置中的重定向URI一致
  - enable_oauth2为true时必填

- **Scopes**: OAuth2权限范围
  - 定义Token的访问权限
  - 一般不需要修改默认配置
  - 包含邮件读写、发送、用户信息读取权限

## 文件保存位置

### 注册成功的账号
- **enable_oauth2 = false**: 保存到 `Results/unlogged_email.txt`
- **enable_oauth2 = true**: 保存到 `Results/logged_email.txt`
- 格式：`<EMAIL>: password`

### Token信息（仅当enable_oauth2=true时）
- 保存到：`Results/outlook_token.txt`
- 格式：`<EMAIL>---password---refresh_token---access_token---expire_at`

## 使用建议

1. **首次使用**：
   - 设置 concurrent_flows = 1, max_tasks = 1 进行测试
   - 确认代理和浏览器配置正确

2. **提高成功率**：
   - 使用指纹浏览器（如AdsPower）
   - 使用住宅IP代理
   - 适当增加各种等待时间
   - 不要设置过高的并发数

3. **验证码处理**：
   - 建议设置 headless = false 以便手动处理验证码
   - 程序会提示何时需要手动完成验证码
   - 有30秒时间完成验证码操作

4. **故障排除**：
   - 如果经常失败，增加 Bot_protection_wait 和 step_delay
   - 如果验证码总是失败，检查代理IP质量
   - 如果浏览器启动失败，检查 browser_path 路径是否正确

## 常见配置示例

### 基础配置（仅注册账号）
```json
{
    "browser_path": "",
    "headless": false,
    "proxy": "http://127.0.0.1:7897",
    "concurrent_flows": 1,
    "max_tasks": 5,
    "Bot_protection_wait": 20,
    "max_captcha_retries": 8,
    "step_delay": 2,
    "enable_oauth2": false
}
```

### 高级配置（获取Token）
```json
{
    "browser_path": "D:/AdsPower Global/AdsPower Global.exe",
    "headless": false,
    "proxy": "http://127.0.0.1:7897",
    "concurrent_flows": 3,
    "max_tasks": 10,
    "Bot_protection_wait": 18,
    "max_captcha_retries": 10,
    "step_delay": 2.5,
    "enable_oauth2": true,
    "client_id": "your-azure-app-id",
    "redirect_url": "http://localhost:8080/callback"
}
```
